import { ethers, upgrades } from "hardhat";

/**
 * Implementation Security Verification Script
 * 
 * This script verifies that implementation contracts have been properly secured
 * with dead address admins, while proxy contracts have the real admins.
 */

async function verifyImplementationSecurity() {
    console.log("🔒 VERIFYING IMPLEMENTATION CONTRACT SECURITY");
    console.log("=".repeat(80));
    
    const contracts = [
        { address: "******************************************", type: "StorageToken", name: "Storage Token" },
        { address: "******************************************", type: "StoragePool", name: "Storage Pool" },
        { address: "******************************************", type: "StakingPool", name: "Staking Pool" },
        { address: "******************************************", type: "TestnetMiningRewards", name: "Testnet Mining" },
        { address: "******************************************", type: "AirdropContract", name: "Airdrop" },
        { address: "******************************************", type: "TokenDistributionEngine", name: "Distribution" },
    ];
    
    const ADMIN_ROLE = ethers.keccak256(ethers.toUtf8Bytes("ADMIN_ROLE"));
    const DEAD_ADDRESS = "******************************************";
    
    for (const contract of contracts) {
        console.log(`\n${"=".repeat(60)}`);
        console.log(`🔍 Analyzing: ${contract.name}`);
        console.log(`📍 Proxy Address: ${contract.address}`);
        
        try {
            // Get implementation address
            const implementationAddress = await upgrades.erc1967.getImplementationAddress(contract.address);
            console.log(`🔧 Implementation Address: ${implementationAddress}`);
            
            // Check proxy admins
            console.log(`\n👥 PROXY ADMINS (Real Admins):`);
            const proxyContract = await ethers.getContractAt(contract.type, contract.address);
            const proxyAdminCount = await proxyContract.adminCount();
            console.log(`   📊 Admin Count: ${proxyAdminCount}`);
            
            // Check known real admins
            const realAdmins = [
                "******************************************",
                "******************************************"
            ];
            
            for (const admin of realAdmins) {
                const hasRole = await proxyContract.hasRole(ADMIN_ROLE, admin);
                console.log(`   ${hasRole ? '✅' : '❌'} ${admin}: ${hasRole ? 'HAS ADMIN ROLE' : 'NO ADMIN ROLE'}`);
            }
            
            // Check implementation admins
            console.log(`\n🔒 IMPLEMENTATION ADMINS (Should be Dead/Dummy):`);
            const implContract = await ethers.getContractAt(contract.type, implementationAddress);
            
            try {
                const implAdminCount = await implContract.adminCount();
                console.log(`   📊 Admin Count: ${implAdminCount}`);
                
                // Check if dead address has admin role
                const deadHasRole = await implContract.hasRole(ADMIN_ROLE, DEAD_ADDRESS);
                console.log(`   ${deadHasRole ? '✅' : '❌'} ${DEAD_ADDRESS}: ${deadHasRole ? 'HAS ADMIN ROLE (GOOD)' : 'NO ADMIN ROLE'}`);
                
                // Check if real admins have role on implementation (they shouldn't)
                for (const admin of realAdmins) {
                    const hasRole = await implContract.hasRole(ADMIN_ROLE, admin);
                    console.log(`   ${hasRole ? '⚠️' : '✅'} ${admin}: ${hasRole ? 'HAS ADMIN ROLE (BAD!)' : 'NO ADMIN ROLE (GOOD)'}`);
                }
                
                // Security assessment
                console.log(`\n🛡️  SECURITY ASSESSMENT:`);
                if (deadHasRole) {
                    console.log(`   ✅ Implementation is secured with dead address admin`);
                } else {
                    console.log(`   ⚠️  Implementation may not be properly secured`);
                }
                
                // Check if implementation can be initialized
                try {
                    // Try to call initialize with dummy params (this should fail if already initialized)
                    await implContract.initialize.staticCall(DEAD_ADDRESS, DEAD_ADDRESS, DEAD_ADDRESS);
                    console.log(`   ⚠️  WARNING: Implementation can still be initialized!`);
                } catch (error: any) {
                    if (error.message.includes("already initialized") || 
                        error.message.includes("Initializable") ||
                        error.message.includes("InvalidInitialization")) {
                        console.log(`   ✅ Implementation is already initialized (secure)`);
                    } else {
                        console.log(`   ❓ Initialize check failed: ${error.message.substring(0, 100)}...`);
                    }
                }
                
            } catch (error: any) {
                console.log(`   ❌ Error checking implementation: ${error.message}`);
            }
            
        } catch (error: any) {
            console.error(`❌ Error processing ${contract.name}: ${error.message}`);
        }
    }
    
    // Summary and recommendations
    console.log(`\n${"=".repeat(80)}`);
    console.log("📋 SECURITY SUMMARY & RECOMMENDATIONS");
    console.log("=".repeat(80));
    
    console.log(`\n✅ WHAT YOU SHOULD SEE (Secure Setup):`);
    console.log(`   1. Proxy contracts have your real admin addresses`);
    console.log(`   2. Implementation contracts have dead address (${DEAD_ADDRESS}) as admin`);
    console.log(`   3. Implementation contracts cannot be re-initialized`);
    console.log(`   4. Real admins do NOT have roles on implementation contracts`);
    
    console.log(`\n⚠️  RED FLAGS (Security Issues):`);
    console.log(`   1. Implementation contracts have real admin addresses`);
    console.log(`   2. Implementation contracts can still be initialized`);
    console.log(`   3. Implementation contracts have no dead address admin`);
    
    console.log(`\n💡 UNDERSTANDING THE ARCHITECTURE:`);
    console.log(`   • Users interact with PROXY addresses (your main contracts)`);
    console.log(`   • Proxy stores all data and delegates calls to implementation`);
    console.log(`   • Implementation contains only code, should be secured with dummy admins`);
    console.log(`   • This prevents attackers from hijacking the implementation contract`);
}

async function quickSecurityCheck(proxyAddress: string, contractType: string) {
    console.log(`🔒 Quick Security Check for ${contractType}`);
    console.log("=".repeat(50));
    
    try {
        const ADMIN_ROLE = ethers.keccak256(ethers.toUtf8Bytes("ADMIN_ROLE"));
        const DEAD_ADDRESS = "******************************************";
        
        // Get implementation
        const implAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
        console.log(`Proxy: ${proxyAddress}`);
        console.log(`Implementation: ${implAddress}`);
        
        // Check proxy
        const proxy = await ethers.getContractAt(contractType, proxyAddress);
        const proxyAdminCount = await proxy.adminCount();
        console.log(`\nProxy admin count: ${proxyAdminCount}`);
        
        // Check implementation
        const impl = await ethers.getContractAt(contractType, implAddress);
        const implAdminCount = await impl.adminCount();
        const deadHasRole = await impl.hasRole(ADMIN_ROLE, DEAD_ADDRESS);
        
        console.log(`Implementation admin count: ${implAdminCount}`);
        console.log(`Dead address is admin: ${deadHasRole ? 'YES ✅' : 'NO ⚠️'}`);
        
        // Try initialize
        try {
            await impl.initialize.staticCall(DEAD_ADDRESS, DEAD_ADDRESS, DEAD_ADDRESS);
            console.log(`Can be initialized: YES ⚠️  (VULNERABLE)`);
        } catch {
            console.log(`Can be initialized: NO ✅ (SECURE)`);
        }
        
    } catch (error: any) {
        console.error(`Error: ${error.message}`);
    }
}

async function main() {
    const mode = process.argv[2];
    
    if (mode === "quick") {
        const address = process.argv[3] || "******************************************";
        const type = process.argv[4] || "StorageToken";
        await quickSecurityCheck(address, type);
    } else {
        await verifyImplementationSecurity();
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Error:", error);
            process.exit(1);
        });
}

export { verifyImplementationSecurity, quickSecurityCheck };
