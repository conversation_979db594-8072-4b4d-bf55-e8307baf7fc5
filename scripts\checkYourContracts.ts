import { ethers, upgrades } from "hardhat";
import { checkImplementationVulnerability, fixImplementationVulnerability, listContractAdmins } from './checkVulnarable';

/**
 * Quick script to check your specific deployed contracts for vulnerabilities
 * 
 * This script uses the actual contract addresses you provided to check for
 * implementation vulnerabilities and automatically fix them.
 */

async function main() {
    console.log("🔍 Checking Your Deployed Contracts for UUPS Implementation Vulnerabilities");
    console.log("=" .repeat(80));
    
    // Your actual deployed contract addresses
    const contractsToCheck = [
        // Core contracts
        { 
            address: "******************************************", 
            type: "StorageToken",
            name: "Storage Token"
        },
        { 
            address: "******************************************", 
            type: "StoragePool",
            name: "Storage Pool"
        },
        { 
            address: "0xD8be67B0f4783aa85Ada89863449b9Bc5D79460b", 
            type: "StakingPool",
            name: "Staking Pool"
        },
        
        // Distribution contracts
        { 
            address: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d", 
            type: "TestnetMiningRewards",
            name: "Testnet Mining Rewards"
        },
        { 
            address: "0x0AF8Bf19C18a3c7352f831cf950CA8971202e4Be", 
            type: "AirdropContract",
            name: "Airdrop Contract"
        },
        { 
            address: "0x0C85A8E992E3Eb04A22027F7E0BC53392A331aC8", 
            type: "TokenDistributionEngine",
            name: "Token Distribution Engine"
        },
    ];
    
    let vulnerableCount = 0;
    let fixedCount = 0;
    let errorCount = 0;
    const results: Array<{
        name: string;
        address: string;
        type: string;
        vulnerable: boolean;
        fixed: boolean;
        error?: string;
        implementationAddress?: string;
        admins?: string[];
    }> = [];
    
    // Check each contract
    for (const contract of contractsToCheck) {
        console.log(`\n${"=".repeat(60)}`);
        console.log(`📋 Checking: ${contract.name}`);
        console.log(`📍 Proxy Address: ${contract.address}`);
        console.log(`🏷️  Contract Type: ${contract.type}`);
        
        try {
            const result = await checkImplementationVulnerability(contract.address, contract.type);

            // Get admin list for this contract
            const admins = await listContractAdmins(contract.address, contract.type);

            const contractResult = {
                name: contract.name,
                address: contract.address,
                type: contract.type,
                vulnerable: result.isVulnerable,
                fixed: false,
                implementationAddress: result.implementationAddress,
                admins: admins
            };
            
            if (result.isVulnerable) {
                vulnerableCount++;
                console.log(`\n⚠️  ${contract.name} implementation is VULNERABLE!`);
                console.log(`🔧 Attempting to fix automatically...`);
                
                try {
                    const fixed = await fixImplementationVulnerability(
                        result.implementationAddress, 
                        contract.type
                    );
                    contractResult.fixed = fixed;
                    if (fixed) {
                        fixedCount++;
                        console.log(`✅ ${contract.name} implementation secured successfully!`);
                    } else {
                        console.log(`❌ Failed to secure ${contract.name} implementation`);
                    }
                } catch (fixError: any) {
                    console.log(`❌ Error fixing ${contract.name}:`, fixError.message);
                    contractResult.error = fixError.message;
                }
            } else {
                console.log(`✅ ${contract.name} implementation is already secure!`);
            }
            
            results.push(contractResult);
            
        } catch (error: any) {
            errorCount++;
            console.error(`❌ Error checking ${contract.name}:`, error.message);
            results.push({
                name: contract.name,
                address: contract.address,
                type: contract.type,
                vulnerable: false,
                fixed: false,
                error: error.message
            });
        }
    }
    
    // Final Summary
    console.log("\n" + "=".repeat(80));
    console.log("📊 FINAL SECURITY AUDIT SUMMARY");
    console.log("=".repeat(80));
    
    console.log(`📋 Total contracts checked: ${contractsToCheck.length}`);
    console.log(`⚠️  Vulnerable implementations found: ${vulnerableCount}`);
    console.log(`🔒 Implementations secured: ${fixedCount}`);
    console.log(`❌ Errors encountered: ${errorCount}`);
    
    if (vulnerableCount === 0 && errorCount === 0) {
        console.log("\n🎉 ALL CONTRACTS ARE SECURE! 🎉");
        console.log("✅ No vulnerabilities found in any implementation contracts.");
    } else if (fixedCount === vulnerableCount && errorCount === 0) {
        console.log("\n🛡️  ALL VULNERABILITIES FIXED! 🛡️");
        console.log("✅ All vulnerable implementations have been secured.");
    } else {
        console.log(`\n⚠️  ATTENTION REQUIRED`);
        if (vulnerableCount - fixedCount > 0) {
            console.log(`❌ ${vulnerableCount - fixedCount} vulnerabilities remain unfixed`);
        }
        if (errorCount > 0) {
            console.log(`❌ ${errorCount} contracts had errors and need manual review`);
        }
    }
    
    // Detailed Results Table
    console.log("\n📋 DETAILED RESULTS:");
    console.log("-".repeat(80));
    console.log("Contract Name".padEnd(25) + "Status".padEnd(15) + "Implementation Address");
    console.log("-".repeat(80));
    
    results.forEach((result) => {
        let status: string;
        if (result.error) {
            status = "❌ ERROR";
        } else if (result.vulnerable) {
            status = result.fixed ? "🔒 FIXED" : "⚠️  VULNERABLE";
        } else {
            status = "✅ SECURE";
        }
        
        const implAddr = result.implementationAddress || "N/A";
        console.log(
            result.name.padEnd(25) + 
            status.padEnd(15) + 
            implAddr
        );
        
        if (result.error) {
            console.log(`   Error: ${result.error}`);
        }
    });
    
    console.log("-".repeat(80));

    // Admin listing section
    console.log("\n" + "=".repeat(80));
    console.log("👥 CONTRACT ADMINS SUMMARY");
    console.log("=".repeat(80));

    results.forEach((result) => {
        console.log(`\n📋 ${result.name} (${result.address}):`);
        if (result.admins && result.admins.length > 0) {
            console.log(`   📊 Admin Count: ${result.admins.length}`);
            result.admins.forEach((admin, adminIndex) => {
                console.log(`   👤 Admin ${adminIndex + 1}: ${admin}`);
            });
        } else {
            console.log(`   ❌ No admins found or error retrieving admin list`);
        }

        if (result.implementationAddress) {
            console.log(`   🔧 Implementation: ${result.implementationAddress}`);
        }
    });

    // Next Steps
    if (vulnerableCount > fixedCount || errorCount > 0) {
        console.log("\n🔧 NEXT STEPS:");
        if (vulnerableCount > fixedCount) {
            console.log("1. Review unfixed vulnerabilities and attempt manual fixes");
            console.log("2. Check network connectivity and gas settings");
        }
        if (errorCount > 0) {
            console.log("3. Investigate contracts with errors");
            console.log("4. Verify contract addresses and types are correct");
        }
        console.log("5. Re-run this script after addressing issues");
    } else {
        console.log("\n🎯 RECOMMENDATIONS:");
        console.log("1. ✅ All implementations are now secure");
        console.log("2. 🔄 Run this check after any future deployments");
        console.log("3. 📝 Document these security measures in your deployment process");
        console.log("4. 🛡️  Consider integrating this check into your CI/CD pipeline");
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Script failed:", error);
            process.exit(1);
        });
}

export { main as checkYourContracts };
