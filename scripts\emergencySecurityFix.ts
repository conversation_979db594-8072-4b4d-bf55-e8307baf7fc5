import { ethers, upgrades } from "hardhat";
import { fixImplementationVulnerability } from './checkVulnarable';

/**
 * EMERGENCY SECURITY FIX
 * 
 * This script immediately checks and fixes the critical vulnerability
 * where implementation contracts are left uninitialized.
 */

async function emergencyCheck() {
    console.log("🚨 EMERGENCY SECURITY CHECK");
    console.log("=".repeat(50));
    
    const vulnerableContracts = [
        { 
            proxy: "******************************************", 
            impl: "******************************************",
            type: "TokenDistributionEngine",
            name: "Distribution Engine"
        },
        { 
            proxy: "******************************************", 
            impl: "******************************************", // Latest implementation
            type: "AirdropContract",
            name: "Airdrop Contract"
        },
        { 
            proxy: "******************************************", 
            impl: "0xdc1bB05397CAC751fA353bb39805A6B16cB08119", // Latest implementation
            type: "TestnetMiningRewards",
            name: "Testnet Mining"
        }
    ];
    
    for (const contract of vulnerableContracts) {
        console.log(`\n${"=".repeat(40)}`);
        console.log(`🔍 Checking: ${contract.name}`);
        console.log(`📍 Proxy: ${contract.proxy}`);
        console.log(`🔧 Implementation: ${contract.impl}`);
        
        try {
            // Check if implementation is initialized
            const impl = await ethers.getContractAt(contract.type, contract.impl);
            
            // Check adminCount
            const adminCount = await impl.adminCount();
            console.log(`📊 Admin Count: ${adminCount}`);
            
            if (Number(adminCount) === 0) {
                console.log(`⚠️  CRITICAL: Implementation is uninitialized!`);
                
                // Try to initialize
                try {
                    const DEAD_ADDRESS = "******************************************";
                    
                    console.log(`🔧 Attempting to secure implementation...`);
                    
                    // Call initialize with dead addresses
                    const tx = await impl.initialize(DEAD_ADDRESS, DEAD_ADDRESS, DEAD_ADDRESS);
                    await tx.wait();
                    
                    console.log(`✅ SUCCESS: Implementation secured!`);
                    console.log(`   Transaction: ${tx.hash}`);
                    
                    // Verify
                    const newAdminCount = await impl.adminCount();
                    console.log(`   New admin count: ${newAdminCount}`);
                    
                } catch (initError: any) {
                    if (initError.message.includes("already initialized")) {
                        console.log(`✅ Implementation was already initialized`);
                    } else {
                        console.log(`❌ Failed to initialize: ${initError.message}`);
                    }
                }
            } else {
                console.log(`✅ Implementation appears initialized (adminCount: ${adminCount})`);
                
                // Double-check by trying to initialize
                try {
                    const DEAD_ADDRESS = "******************************************";
                    await impl.initialize.staticCall(DEAD_ADDRESS, DEAD_ADDRESS, DEAD_ADDRESS);
                    console.log(`⚠️  WARNING: Can still be initialized despite adminCount > 0!`);
                } catch (error: any) {
                    if (error.message.includes("already initialized")) {
                        console.log(`✅ Confirmed: Cannot be re-initialized`);
                    } else {
                        console.log(`❓ Initialize check failed: ${error.message.substring(0, 50)}...`);
                    }
                }
            }
            
        } catch (error: any) {
            console.log(`❌ Error checking ${contract.name}: ${error.message}`);
        }
    }
}

async function quickVulnerabilityCheck(implementationAddress: string, contractType: string) {
    console.log(`🔍 Quick check: ${contractType} at ${implementationAddress}`);
    
    try {
        const impl = await ethers.getContractAt(contractType, implementationAddress);
        
        // Check 1: adminCount
        const adminCount = await impl.adminCount();
        console.log(`📊 Admin Count: ${adminCount}`);
        
        // Check 2: Try initialize
        const DEAD_ADDRESS = "******************************************";
        try {
            await impl.initialize.staticCall(DEAD_ADDRESS, DEAD_ADDRESS, DEAD_ADDRESS);
            console.log(`⚠️  VULNERABLE: Can be initialized`);
            return true; // Vulnerable
        } catch (error: any) {
            if (error.message.includes("already initialized")) {
                console.log(`✅ SAFE: Already initialized`);
                return false; // Safe
            } else {
                console.log(`❓ Unknown: ${error.message.substring(0, 50)}...`);
                return Number(adminCount) === 0; // Vulnerable if adminCount is 0
            }
        }
        
    } catch (error: any) {
        console.log(`❌ Error: ${error.message}`);
        return true; // Assume vulnerable if can't check
    }
}

async function fixSpecificImplementation(implementationAddress: string, contractType: string) {
    console.log(`🔧 Fixing ${contractType} at ${implementationAddress}`);
    
    try {
        const impl = await ethers.getContractAt(contractType, implementationAddress);
        const DEAD_ADDRESS = "******************************************";
        
        console.log(`📝 Calling initialize with dead addresses...`);
        const tx = await impl.initialize(DEAD_ADDRESS, DEAD_ADDRESS, DEAD_ADDRESS);
        await tx.wait();
        
        console.log(`✅ Fixed! Transaction: ${tx.hash}`);
        
        // Verify
        const adminCount = await impl.adminCount();
        console.log(`📊 New admin count: ${adminCount}`);
        
        return true;
    } catch (error: any) {
        if (error.message.includes("already initialized")) {
            console.log(`✅ Already initialized`);
            return true;
        } else {
            console.log(`❌ Failed: ${error.message}`);
            return false;
        }
    }
}

async function main() {
    const action = process.argv[2];
    
    switch (action) {
        case "check":
            await emergencyCheck();
            break;
            
        case "fix":
            const implAddress = process.argv[3];
            const contractType = process.argv[4];
            if (!implAddress || !contractType) {
                console.log("Usage: npm run fix <implementation-address> <contract-type>");
                console.log("Example: npm run fix ****************************************** TokenDistributionEngine");
                return;
            }
            await fixSpecificImplementation(implAddress, contractType);
            break;
            
        case "quick":
            const quickImplAddress = process.argv[3];
            const quickContractType = process.argv[4];
            if (!quickImplAddress || !quickContractType) {
                console.log("Usage: npm run quick <implementation-address> <contract-type>");
                return;
            }
            const isVulnerable = await quickVulnerabilityCheck(quickImplAddress, quickContractType);
            console.log(`Result: ${isVulnerable ? 'VULNERABLE ⚠️' : 'SAFE ✅'}`);
            break;
            
        default:
            console.log("🚨 EMERGENCY SECURITY ACTIONS:");
            console.log("");
            console.log("1. Check all vulnerable contracts:");
            console.log("   npx hardhat run scripts/emergencySecurityFix.ts -- check");
            console.log("");
            console.log("2. Fix specific implementation:");
            console.log("   npx hardhat run scripts/emergencySecurityFix.ts -- fix ****************************************** TokenDistributionEngine");
            console.log("");
            console.log("3. Quick vulnerability check:");
            console.log("   npx hardhat run scripts/emergencySecurityFix.ts -- quick ****************************************** TokenDistributionEngine");
            console.log("");
            console.log("⚠️  CRITICAL: These implementations may be vulnerable to takeover!");
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Error:", error);
            process.exit(1);
        });
}

export { emergencyCheck, quickVulnerabilityCheck, fixSpecificImplementation };
