import { ethers } from "hardhat";

/**
 * ERC1967Proxy Front-Running Attack Detection Script
 * 
 * This script checks if your proxy contracts were compromised by the initialization
 * front-running/backdoor attack that targeted uninitialized proxies.
 * 
 * The attack works by:
 * 1. Monitoring mempool for proxy deployments
 * 2. Front-running the initialize() call
 * 3. Setting malicious admin/implementation
 * 4. Spoofing events to hide the attack
 */

interface ProxyInfo {
    name: string;
    proxyAddress: string;
    expectedAdmin: string;
    expectedOwner: string;
    contractType: string;
}

// ERC1967 storage slots
const ADMIN_SLOT = "0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103";
const IMPLEMENTATION_SLOT = "0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc";
const BEACON_SLOT = "0xa3f0ad74e5423aebfd80d3ef4346578335a9a72aeaee59ff6cb3582b35133d50";

async function checkProxyStorage(proxyAddress: string): Promise<{
    admin: string;
    implementation: string;
    beacon?: string;
}> {
    console.log(`\n🔍 Checking storage slots for ${proxyAddress}...`);

    try {
        // Get provider
        const provider = ethers.provider;

        // Read ERC1967 storage slots using the correct method
        const adminSlot = await provider.send("eth_getStorageAt", [proxyAddress, ADMIN_SLOT, "latest"]);
        const implSlot = await provider.send("eth_getStorageAt", [proxyAddress, IMPLEMENTATION_SLOT, "latest"]);
        const beaconSlot = await provider.send("eth_getStorageAt", [proxyAddress, BEACON_SLOT, "latest"]);

        // Convert to addresses (last 20 bytes)
        const admin = adminSlot !== "******************************************000000000000000000000000"
            ? ethers.getAddress("0x" + adminSlot.slice(26))
            : "******************************************";

        const implementation = implSlot !== "******************************************000000000000000000000000"
            ? ethers.getAddress("0x" + implSlot.slice(26))
            : "******************************************";

        const beacon = beaconSlot !== "******************************************000000000000000000000000"
            ? ethers.getAddress("0x" + beaconSlot.slice(26))
            : undefined;

        console.log(`   📍 Admin: ${admin}`);
        console.log(`   🔧 Implementation: ${implementation}`);
        if (beacon) console.log(`   📡 Beacon: ${beacon}`);

        return { admin, implementation, beacon };
    } catch (error: any) {
        console.log(`   ❌ Error reading storage: ${error.message}`);

        // Fallback: try using upgrades helper
        try {
            console.log(`   🔄 Trying alternative method...`);
            const { upgrades } = await import("hardhat");
            const implementation = await upgrades.erc1967.getImplementationAddress(proxyAddress);
            const admin = await upgrades.erc1967.getAdminAddress(proxyAddress);

            console.log(`   📍 Admin: ${admin}`);
            console.log(`   🔧 Implementation: ${implementation}`);

            return { admin, implementation };
        } catch (fallbackError: any) {
            console.log(`   ❌ Fallback also failed: ${fallbackError.message}`);
            return {
                admin: "ERROR",
                implementation: "ERROR"
            };
        }
    }
}

async function checkInitializationHistory(proxyAddress: string, contractType: string): Promise<{
    isInitialized: boolean;
    initializationTx?: string;
    initializationBlock?: number;
    initializationCaller?: string;
    suspiciousActivity: boolean;
}> {
    console.log(`\n📅 Checking initialization history...`);
    
    try {
        // Get contract instance
        const contract = await ethers.getContractAt(contractType, proxyAddress);
        
        // Check if initialized by trying to call initialize
        let isInitialized = false;
        try {
            // This should fail if already initialized
            await contract.initialize.staticCall(
                "******************************************",
                "******************************************", 
                "******************************************"
            );
            console.log(`   ⚠️  Contract can still be initialized!`);
        } catch (error: any) {
            if (error.message.includes("already initialized") || 
                error.message.includes("Initializable") ||
                error.message.includes("InvalidInitialization")) {
                isInitialized = true;
                console.log(`   ✅ Contract is properly initialized`);
            }
        }
        
        // Search for Initialized events
        const currentBlock = await ethers.provider.getBlockNumber();
        const fromBlock = Math.max(0, currentBlock - 100000); // Last 100k blocks
        
        try {
            const filter = contract.filters.Initialized();
            const events = await contract.queryFilter(filter, fromBlock, currentBlock);
            
            if (events.length > 0) {
                const initEvent = events[0];
                console.log(`   📅 Initialized at block: ${initEvent.blockNumber}`);
                console.log(`   🔗 Transaction: ${initEvent.transactionHash}`);
                
                // Get transaction details
                try {
                    const tx = await ethers.provider.send("eth_getTransactionByHash", [initEvent.transactionHash]);
                    if (tx && tx.from) {
                        console.log(`   👤 Initialized by: ${tx.from}`);
                        return {
                            isInitialized,
                            initializationTx: initEvent.transactionHash,
                            initializationBlock: initEvent.blockNumber,
                            initializationCaller: tx.from,
                            suspiciousActivity: false
                        };
                    }
                } catch (txError) {
                    console.log(`   ❌ Could not get transaction details`);
                }
            } else {
                console.log(`   ❓ No Initialized events found`);
            }
        } catch (error) {
            console.log(`   ❌ Could not search for events`);
        }
        
        return {
            isInitialized,
            suspiciousActivity: false
        };
        
    } catch (error: any) {
        console.log(`   ❌ Error checking initialization: ${error.message}`);
        return {
            isInitialized: false,
            suspiciousActivity: true
        };
    }
}

async function verifyImplementationBytecode(implementationAddress: string): Promise<{
    hasCode: boolean;
    isLegitimate: boolean;
    codeSize: number;
}> {
    console.log(`\n🔍 Verifying implementation bytecode...`);

    try {
        // Use provider.send for better compatibility
        const code = await ethers.provider.send("eth_getCode", [implementationAddress, "latest"]);
        const codeSize = (code.length - 2) / 2; // Remove 0x and convert to bytes

        console.log(`   📏 Code size: ${codeSize} bytes`);

        if (code === "0x" || code === "0x0") {
            console.log(`   ❌ No code at implementation address!`);
            return { hasCode: false, isLegitimate: false, codeSize: 0 };
        }

        // Basic legitimacy checks
        const hasSelectors = code.length > 1000; // Reasonable size for a real contract
        const isLegitimate = hasSelectors && codeSize > 500; // Basic heuristics

        console.log(`   ${isLegitimate ? '✅' : '⚠️'} Implementation appears ${isLegitimate ? 'legitimate' : 'suspicious'}`);

        return { hasCode: true, isLegitimate, codeSize };

    } catch (error: any) {
        console.log(`   ❌ Error verifying bytecode: ${error.message}`);

        // Fallback: try direct method
        try {
            console.log(`   🔄 Trying fallback method...`);
            const provider = ethers.provider;
            const code = await provider.getCode(implementationAddress);
            const codeSize = (code.length - 2) / 2;

            if (code === "0x") {
                return { hasCode: false, isLegitimate: false, codeSize: 0 };
            }

            const isLegitimate = codeSize > 500;
            console.log(`   ${isLegitimate ? '✅' : '⚠️'} Implementation appears ${isLegitimate ? 'legitimate' : 'suspicious'}`);

            return { hasCode: true, isLegitimate, codeSize };
        } catch (fallbackError) {
            console.log(`   ❌ Fallback failed, assuming no code`);
            return { hasCode: false, isLegitimate: false, codeSize: 0 };
        }
    }
}

async function checkSingleProxy(proxyInfo: ProxyInfo): Promise<{
    isCompromised: boolean;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    issues: string[];
}> {
    console.log(`\n${"=".repeat(60)}`);
    console.log(`🔍 ANALYZING: ${proxyInfo.name}`);
    console.log(`📍 Proxy: ${proxyInfo.proxyAddress}`);
    console.log(`🏷️  Type: ${proxyInfo.contractType}`);
    console.log("=".repeat(60));
    
    const issues: string[] = [];
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    
    // 1. Check storage slots
    const storage = await checkProxyStorage(proxyInfo.proxyAddress);
    
    // 2. Verify admin
    if (storage.admin !== "******************************************") {
        if (storage.admin !== proxyInfo.expectedAdmin && storage.admin !== proxyInfo.expectedOwner) {
            issues.push(`Unexpected admin: ${storage.admin}`);
            riskLevel = 'CRITICAL';
        } else {
            console.log(`   ✅ Admin matches expected address`);
        }
    }
    
    // 3. Check implementation
    if (storage.implementation === "******************************************") {
        issues.push("No implementation set");
        riskLevel = 'HIGH';
    } else {
        // Verify implementation bytecode
        const bytecodeCheck = await verifyImplementationBytecode(storage.implementation);
        if (!bytecodeCheck.hasCode) {
            issues.push("Implementation has no code");
            riskLevel = 'CRITICAL';
        } else if (!bytecodeCheck.isLegitimate) {
            issues.push("Implementation appears suspicious");
            riskLevel = 'HIGH';
        }
    }
    
    // 4. Check initialization history
    const initHistory = await checkInitializationHistory(proxyInfo.proxyAddress, proxyInfo.contractType);
    
    if (!initHistory.isInitialized) {
        issues.push("Contract is not initialized");
        riskLevel = 'CRITICAL';
    }
    
    if (initHistory.suspiciousActivity) {
        issues.push("Suspicious initialization activity detected");
        if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
    }
    
    if (initHistory.initializationCaller && 
        initHistory.initializationCaller !== proxyInfo.expectedAdmin && 
        initHistory.initializationCaller !== proxyInfo.expectedOwner) {
        issues.push(`Initialized by unexpected address: ${initHistory.initializationCaller}`);
        riskLevel = 'HIGH';
    }
    
    // 5. Overall assessment
    const isCompromised = riskLevel === 'CRITICAL' || riskLevel === 'HIGH';
    
    console.log(`\n🛡️  SECURITY ASSESSMENT:`);
    console.log(`   Risk Level: ${riskLevel}`);
    console.log(`   Compromised: ${isCompromised ? 'YES ⚠️' : 'NO ✅'}`);
    
    if (issues.length > 0) {
        console.log(`   Issues Found:`);
        issues.forEach(issue => console.log(`     - ${issue}`));
    } else {
        console.log(`   ✅ No security issues detected`);
    }
    
    return { isCompromised, riskLevel, issues };
}

async function main() {
    console.log("🚨 ERC1967PROXY FRONT-RUNNING ATTACK DETECTION");
    console.log("=".repeat(80));
    console.log("Checking for initialization front-running/backdoor attacks...");
    
    // Your deployed contracts
    const proxiesToCheck: ProxyInfo[] = [
        {
            name: "Storage Token",
            proxyAddress: "0x9e12735d77c72c5C3670636D428f2F3815d8A4cB",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "StorageToken"
        },
        {
            name: "Storage Pool",
            proxyAddress: "0x2e757c35680756cdF8e6AE3f8a346D12b4e3773D",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "StoragePool"
        },
        {
            name: "Staking Pool",
            proxyAddress: "0xD8be67B0f4783aa85Ada89863449b9Bc5D79460b",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "StakingPool"
        },
        {
            name: "Testnet Mining Rewards",
            proxyAddress: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "TestnetMiningRewards"
        },
        {
            name: "Airdrop Contract",
            proxyAddress: "0x0AF8Bf19C18a3c7352f831cf950CA8971202e4Be",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "AirdropContract"
        },
        {
            name: "Token Distribution Engine",
            proxyAddress: "0x0C85A8E992E3Eb04A22027F7E0BC53392A331aC8",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "TokenDistributionEngine"
        }
    ];
    
    const results = [];
    let compromisedCount = 0;
    let criticalCount = 0;
    
    // Check each proxy
    for (const proxyInfo of proxiesToCheck) {
        const result = await checkSingleProxy(proxyInfo);
        results.push({
            name: proxyInfo.name,
            address: proxyInfo.proxyAddress,
            ...result
        });
        
        if (result.isCompromised) compromisedCount++;
        if (result.riskLevel === 'CRITICAL') criticalCount++;
    }
    
    // Final summary
    console.log(`\n${"=".repeat(80)}`);
    console.log("📊 FINAL SECURITY REPORT");
    console.log("=".repeat(80));
    
    console.log(`📋 Total contracts checked: ${proxiesToCheck.length}`);
    console.log(`⚠️  Compromised contracts: ${compromisedCount}`);
    console.log(`🚨 Critical risk contracts: ${criticalCount}`);
    
    if (compromisedCount === 0) {
        console.log(`\n🎉 ALL CONTRACTS APPEAR SECURE! 🎉`);
        console.log(`✅ No evidence of front-running attacks detected`);
    } else {
        console.log(`\n🚨 SECURITY ALERT! 🚨`);
        console.log(`${compromisedCount} contract(s) may be compromised`);
    }
    
    // Detailed results
    console.log(`\n📋 DETAILED RESULTS:`);
    console.log("-".repeat(80));
    results.forEach((result, index) => {
        const statusIcon = result.isCompromised ? "🚨" : "✅";
        console.log(`${index + 1}. ${result.name}: ${statusIcon} ${result.riskLevel}`);
        if (result.issues.length > 0) {
            result.issues.forEach(issue => console.log(`     - ${issue}`));
        }
    });
    
    if (compromisedCount > 0) {
        console.log(`\n🆘 IMMEDIATE ACTIONS REQUIRED:`);
        console.log(`1. 🛑 DO NOT interact with compromised contracts`);
        console.log(`2. 📞 Contact security teams: Seal 911, Dedaub`);
        console.log(`3. 🔍 Verify findings manually on block explorer`);
        console.log(`4. 💰 Check if funds are still safe`);
        console.log(`5. 🚀 Plan migration if necessary`);
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Error:", error);
            process.exit(1);
        });
}

export { checkSingleProxy, checkProxyStorage, checkInitializationHistory };
