import { ethers, upgrades } from "hardhat";
import { checkImplementationVulnerability, fixImplementationVulnerability } from '../checkVulnarable';

/**
 * Example: Secure UUPS Deployment with Automatic Vulnerability Fix
 * 
 * This example shows how to integrate the vulnerability checker
 * into your deployment scripts to automatically secure implementations.
 */

async function secureDeployStorageToken() {
    console.log("🚀 Deploying StorageToken with automatic security fix...");
    
    const initialOwner = process.env.INITIAL_OWNER || "******************************************";
    const initialAdmin = process.env.INITIAL_ADMIN || "******************************************";
    const initialSupply = ethers.parseEther("1000000"); // 1M tokens
    
    // 1. Deploy the proxy normally
    console.log("📦 Deploying StorageToken proxy...");
    const StorageToken = await ethers.getContractFactory("StorageToken");
    const storageToken = await upgrades.deployProxy(
        StorageToken,
        [initialOwner, initialAdmin, initialSupply],
        { kind: 'uups', initializer: 'initialize' }
    );
    await storageToken.waitForDeployment();
    
    const proxyAddress = await storageToken.getAddress();
    console.log("✅ StorageToken proxy deployed to:", proxyAddress);
    
    // 2. Automatically check and fix implementation vulnerability
    console.log("\n🛡️  Securing implementation contract...");
    const result = await checkImplementationVulnerability(proxyAddress, "StorageToken");
    
    if (result.isVulnerable) {
        console.log("⚠️  Implementation is vulnerable - fixing automatically...");
        const fixed = await fixImplementationVulnerability(result.implementationAddress, "StorageToken");
        
        if (fixed) {
            console.log("✅ Implementation secured successfully!");
        } else {
            console.error("❌ Failed to secure implementation - manual intervention required!");
            process.exit(1);
        }
    } else {
        console.log("✅ Implementation is already secure!");
    }
    
    // 3. Verify the deployment
    console.log("\n🔍 Verifying deployment...");
    const tokenName = await storageToken.name();
    const tokenSymbol = await storageToken.symbol();
    const totalSupply = await storageToken.totalSupply();
    
    console.log("Token Name:", tokenName);
    console.log("Token Symbol:", tokenSymbol);
    console.log("Total Supply:", ethers.formatEther(totalSupply));
    
    return { proxy: storageToken, proxyAddress, implementationAddress: result.implementationAddress };
}

async function secureDeployStoragePool() {
    console.log("\n🚀 Deploying StoragePool with automatic security fix...");
    
    const tokenAddress = process.env.TOKEN_ADDRESS || "******************************************";
    const stakingPoolAddress = process.env.STAKING_POOL_ADDRESS || "******************************************";
    const initialOwner = process.env.INITIAL_OWNER || "******************************************";
    const initialAdmin = process.env.INITIAL_ADMIN || "******************************************";
    
    // 1. Deploy the proxy
    console.log("📦 Deploying StoragePool proxy...");
    const StoragePool = await ethers.getContractFactory("StoragePool");
    const storagePool = await upgrades.deployProxy(
        StoragePool,
        [tokenAddress, stakingPoolAddress, initialOwner, initialAdmin],
        { kind: 'uups', initializer: 'initialize' }
    );
    await storagePool.waitForDeployment();
    
    const proxyAddress = await storagePool.getAddress();
    console.log("✅ StoragePool proxy deployed to:", proxyAddress);
    
    // 2. Secure the implementation
    console.log("\n🛡️  Securing implementation contract...");
    const result = await checkImplementationVulnerability(proxyAddress, "StoragePool");
    
    if (result.isVulnerable) {
        await fixImplementationVulnerability(result.implementationAddress, "StoragePool");
        console.log("✅ Implementation secured!");
    }
    
    return { proxy: storagePool, proxyAddress, implementationAddress: result.implementationAddress };
}

async function deployAndSecureMultipleContracts() {
    console.log("🏗️  Deploying and securing multiple contracts...");
    
    const results = [];
    
    try {
        // Deploy StorageToken
        const tokenResult = await secureDeployStorageToken();
        results.push({ name: "StorageToken", ...tokenResult });
        
        // Deploy StoragePool (using the token we just deployed)
        process.env.TOKEN_ADDRESS = tokenResult.proxyAddress;
        const poolResult = await secureDeployStoragePool();
        results.push({ name: "StoragePool", ...poolResult });
        
        // Summary
        console.log("\n" + "=".repeat(60));
        console.log("📋 DEPLOYMENT SUMMARY");
        console.log("=".repeat(60));
        
        results.forEach((result, index) => {
            console.log(`${index + 1}. ${result.name}:`);
            console.log(`   Proxy: ${result.proxyAddress}`);
            console.log(`   Implementation: ${result.implementationAddress}`);
            console.log(`   Status: ✅ Secured`);
        });
        
        console.log("\n🎉 All contracts deployed and secured successfully!");
        
    } catch (error) {
        console.error("❌ Deployment failed:", error);
        process.exit(1);
    }
}

// Utility function to check existing deployments
async function auditExistingDeployments() {
    console.log("🔍 Auditing existing deployments for vulnerabilities...");
    
    const existingContracts = [
        // Add your existing contract addresses here
        // { address: "0x...", type: "StorageToken" },
        // { address: "0x...", type: "StoragePool" },

        // Example addresses from your deployments:
        // { address: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d", type: "TestnetMiningRewards" },
        // { address: "0x0AF8Bf19C18a3c7352f831cf950CA8971202e4Be", type: "AirdropContract" },
        // { address: "0x0C85A8E992E3Eb04A22027F7E0BC53392A331aC8", type: "TokenDistributionEngine" },
    ];
    
    if (existingContracts.length === 0) {
        console.log("📝 No existing contracts to audit. Update the existingContracts array.");
        return;
    }
    
    let vulnerableCount = 0;
    
    for (const contract of existingContracts) {
        const result = await checkImplementationVulnerability(contract.address, contract.type);
        if (result.isVulnerable) {
            vulnerableCount++;
            console.log(`⚠️  ${contract.type} at ${contract.address} is VULNERABLE!`);
            
            // Optionally auto-fix
            // await fixImplementationVulnerability(result.implementationAddress, contract.type);
        }
    }
    
    if (vulnerableCount === 0) {
        console.log("✅ All existing deployments are secure!");
    } else {
        console.log(`⚠️  Found ${vulnerableCount} vulnerable implementations!`);
    }
}

async function main() {
    const command = process.argv[2];
    
    switch (command) {
        case "token":
            await secureDeployStorageToken();
            break;
        case "pool":
            await secureDeployStoragePool();
            break;
        case "all":
            await deployAndSecureMultipleContracts();
            break;
        case "audit":
            await auditExistingDeployments();
            break;
        default:
            console.log("Usage:");
            console.log("  npx hardhat run scripts/examples/secureDeployment.ts -- token   # Deploy StorageToken");
            console.log("  npx hardhat run scripts/examples/secureDeployment.ts -- pool    # Deploy StoragePool");
            console.log("  npx hardhat run scripts/examples/secureDeployment.ts -- all     # Deploy multiple contracts");
            console.log("  npx hardhat run scripts/examples/secureDeployment.ts -- audit   # Audit existing deployments");
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error(error);
            process.exit(1);
        });
}

export {
    secureDeployStorageToken,
    secureDeployStoragePool,
    deployAndSecureMultipleContracts,
    auditExistingDeployments
};
