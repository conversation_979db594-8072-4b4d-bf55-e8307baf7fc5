import { ethers, upgrades } from "hardhat";

/**
 * Simplified ERC1967Proxy Attack Detection Script
 * 
 * This script uses OpenZeppelin's upgrades helper to check for proxy attacks
 * without relying on low-level provider methods that might have compatibility issues.
 */

interface ProxyInfo {
    name: string;
    proxyAddress: string;
    expectedAdmin: string;
    expectedOwner: string;
    contractType: string;
}

async function checkProxyWithUpgrades(proxyInfo: ProxyInfo) {
    console.log(`\n${"=".repeat(60)}`);
    console.log(`🔍 CHECKING: ${proxyInfo.name}`);
    console.log(`📍 Proxy: ${proxyInfo.proxyAddress}`);
    console.log("=".repeat(60));
    
    const issues: string[] = [];
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';
    
    try {
        // 1. Get implementation and admin using OpenZeppelin upgrades
        console.log(`\n📋 Getting proxy information...`);
        const implementationAddress = await upgrades.erc1967.getImplementationAddress(proxyInfo.proxyAddress);
        console.log(`   🔧 Implementation: ${implementationAddress}`);
        
        let adminAddress = "N/A";
        try {
            adminAddress = await upgrades.erc1967.getAdminAddress(proxyInfo.proxyAddress);
            console.log(`   👤 Admin: ${adminAddress}`);
        } catch (error) {
            console.log(`   👤 Admin: Not set (UUPS proxy)`);
        }
        
        // 2. Check if implementation has code
        console.log(`\n🔍 Verifying implementation...`);
        const implementationCode = await ethers.provider.getCode(implementationAddress);
        if (implementationCode === "0x") {
            issues.push("Implementation has no code");
            riskLevel = 'CRITICAL';
            console.log(`   ❌ No code at implementation address!`);
        } else {
            const codeSize = (implementationCode.length - 2) / 2;
            console.log(`   ✅ Implementation has code (${codeSize} bytes)`);
            
            if (codeSize < 500) {
                issues.push("Implementation code is suspiciously small");
                riskLevel = 'HIGH';
            }
        }
        
        // 3. Check admin (if applicable)
        if (adminAddress !== "N/A") {
            if (adminAddress !== proxyInfo.expectedAdmin && adminAddress !== proxyInfo.expectedOwner) {
                issues.push(`Unexpected admin: ${adminAddress}`);
                riskLevel = 'CRITICAL';
                console.log(`   ⚠️  Admin does not match expected addresses!`);
            } else {
                console.log(`   ✅ Admin matches expected address`);
            }
        }
        
        // 4. Check if proxy contract is initialized
        console.log(`\n🔍 Checking proxy initialization status...`);
        try {
            const contract = await ethers.getContractAt(proxyInfo.contractType, proxyInfo.proxyAddress);

            // Check admin count first (more reliable indicator)
            let proxyAdminCount = 0;
            try {
                proxyAdminCount = Number(await contract.adminCount());
                console.log(`   📊 Proxy admin count: ${proxyAdminCount}`);
            } catch (error) {
                console.log(`   ❓ Could not check proxy admin count`);
            }

            // Try to call initialize - should fail if already initialized
            let canInitialize = false;
            try {
                await contract.initialize.staticCall(
                    "******************************************",
                    "******************************************",
                    "******************************************"
                );
                canInitialize = true;
                console.log(`   ⚠️  Proxy can still be initialized!`);
            } catch (error: any) {
                if (error.message.includes("already initialized") ||
                    error.message.includes("Initializable") ||
                    error.message.includes("InvalidInitialization")) {
                    console.log(`   ✅ Proxy is properly initialized`);
                } else {
                    console.log(`   ❓ Proxy initialize check inconclusive: ${error.message.substring(0, 50)}...`);
                }
            }

            // Assessment logic: proxy is considered initialized if adminCount > 0 OR initialize fails
            const isProxyInitialized = proxyAdminCount > 0 || !canInitialize;

            if (!isProxyInitialized) {
                issues.push("Proxy contract appears uninitialized");
                riskLevel = 'CRITICAL';
            }

        } catch (error: any) {
            console.log(`   ❌ Could not check proxy: ${error.message.substring(0, 50)}...`);
            issues.push("Could not verify proxy initialization");
            if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
        }

        // 5. Check implementation initialization (security measure)
        console.log(`\n🔍 Checking implementation security...`);
        try {
            const implContract = await ethers.getContractAt(proxyInfo.contractType, implementationAddress);

            let implAdminCount = 0;
            try {
                implAdminCount = Number(await implContract.adminCount());
                console.log(`   📊 Implementation admin count: ${implAdminCount}`);
            } catch (error) {
                console.log(`   ❓ Could not check implementation admin count`);
            }

            // Try to initialize implementation
            try {
                await implContract.initialize.staticCall(
                    "******************************************",
                    "******************************************",
                    "******************************************"
                );
                issues.push("Implementation can still be initialized - SECURITY RISK!");
                riskLevel = 'CRITICAL';
                console.log(`   ⚠️  Implementation can still be initialized - VULNERABLE!`);
            } catch (error: any) {
                if (error.message.includes("already initialized") ||
                    error.message.includes("Initializable") ||
                    error.message.includes("InvalidInitialization")) {
                    console.log(`   ✅ Implementation is properly secured`);
                } else if (implAdminCount === 0) {
                    issues.push("Implementation appears uninitialized (adminCount = 0)");
                    riskLevel = 'CRITICAL';
                    console.log(`   ⚠️  Implementation appears uninitialized!`);
                } else {
                    console.log(`   ❓ Implementation check inconclusive but adminCount > 0`);
                }
            }

        } catch (error: any) {
            console.log(`   ❌ Could not check implementation: ${error.message.substring(0, 50)}...`);
            issues.push("Could not verify implementation security");
            if (riskLevel === 'LOW') riskLevel = 'MEDIUM';
        }
        
        // 6. Basic deployment verification
        console.log(`\n🔍 Basic deployment checks...`);
        
        // Check if proxy has code
        const proxyCode = await ethers.provider.getCode(proxyInfo.proxyAddress);
        if (proxyCode === "0x") {
            issues.push("Proxy has no code");
            riskLevel = 'CRITICAL';
            console.log(`   ❌ Proxy has no code!`);
        } else {
            console.log(`   ✅ Proxy has code`);
        }
        
        // 6. Assessment
        const isCompromised = riskLevel === 'CRITICAL' || riskLevel === 'HIGH';
        
        console.log(`\n🛡️  SECURITY ASSESSMENT:`);
        console.log(`   Risk Level: ${riskLevel}`);
        console.log(`   Compromised: ${isCompromised ? 'POSSIBLY ⚠️' : 'NO ✅'}`);
        
        if (issues.length > 0) {
            console.log(`   Issues Found:`);
            issues.forEach(issue => console.log(`     - ${issue}`));
        } else {
            console.log(`   ✅ No security issues detected`);
        }
        
        return {
            name: proxyInfo.name,
            address: proxyInfo.proxyAddress,
            implementationAddress,
            adminAddress,
            isCompromised,
            riskLevel,
            issues
        };
        
    } catch (error: any) {
        console.log(`\n❌ Error checking proxy: ${error.message}`);
        return {
            name: proxyInfo.name,
            address: proxyInfo.proxyAddress,
            implementationAddress: "ERROR",
            adminAddress: "ERROR",
            isCompromised: true,
            riskLevel: 'CRITICAL' as const,
            issues: [`Failed to check proxy: ${error.message}`]
        };
    }
}

async function main() {
    console.log("🚨 SIMPLIFIED ERC1967PROXY ATTACK DETECTION");
    console.log("=".repeat(80));
    console.log("Checking for initialization front-running/backdoor attacks...");
    
    // Your deployed contracts
    const proxiesToCheck: ProxyInfo[] = [
        {
            name: "Storage Token",
            proxyAddress: "0x9e12735d77c72c5C3670636D428f2F3815d8A4cB",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "StorageToken"
        },
        {
            name: "Storage Pool",
            proxyAddress: "0x2e757c35680756cdF8e6AE3f8a346D12b4e3773D",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "StoragePool"
        },
        {
            name: "Staking Pool",
            proxyAddress: "0xD8be67B0f4783aa85Ada89863449b9Bc5D79460b",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "StakingPool"
        },
        {
            name: "Testnet Mining Rewards",
            proxyAddress: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "TestnetMiningRewards"
        },
        {
            name: "Airdrop Contract",
            proxyAddress: "0x0AF8Bf19C18a3c7352f831cf950CA8971202e4Be",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "AirdropContract"
        },
        {
            name: "Token Distribution Engine",
            proxyAddress: "0x0C85A8E992E3Eb04A22027F7E0BC53392A331aC8",
            expectedAdmin: "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446",
            expectedOwner: "0x383a6A34C623C02dcf9BB7069FAE4482967fb713",
            contractType: "TokenDistributionEngine"
        }
    ];
    
    const results = [];
    let compromisedCount = 0;
    let criticalCount = 0;
    
    // Check each proxy
    for (const proxyInfo of proxiesToCheck) {
        const result = await checkProxyWithUpgrades(proxyInfo);
        results.push(result);
        
        if (result.isCompromised) compromisedCount++;
        if (result.riskLevel === 'CRITICAL') criticalCount++;
    }
    
    // Final summary
    console.log(`\n${"=".repeat(80)}`);
    console.log("📊 FINAL SECURITY REPORT");
    console.log("=".repeat(80));
    
    console.log(`📋 Total contracts checked: ${proxiesToCheck.length}`);
    console.log(`⚠️  Potentially compromised: ${compromisedCount}`);
    console.log(`🚨 Critical risk: ${criticalCount}`);
    
    if (compromisedCount === 0) {
        console.log(`\n🎉 ALL CONTRACTS APPEAR SECURE! 🎉`);
        console.log(`✅ No evidence of front-running attacks detected`);
    } else {
        console.log(`\n🚨 SECURITY ALERT! 🚨`);
        console.log(`${compromisedCount} contract(s) may have issues`);
    }
    
    // Detailed results table
    console.log(`\n📋 SUMMARY TABLE:`);
    console.log("-".repeat(80));
    console.log("Contract".padEnd(25) + "Risk Level".padEnd(15) + "Status");
    console.log("-".repeat(80));
    
    results.forEach((result) => {
        const statusIcon = result.isCompromised ? "⚠️" : "✅";
        const status = result.isCompromised ? "NEEDS REVIEW" : "SECURE";
        console.log(
            result.name.padEnd(25) + 
            result.riskLevel.padEnd(15) + 
            `${statusIcon} ${status}`
        );
    });
    
    console.log("-".repeat(80));
    
    if (compromisedCount > 0) {
        console.log(`\n🆘 RECOMMENDED ACTIONS:`);
        console.log(`1. 🔍 Review contracts with HIGH/CRITICAL risk`);
        console.log(`2. 🛑 Avoid interacting with suspicious contracts`);
        console.log(`3. 📞 Consider getting security audit if issues found`);
        console.log(`4. 🔧 Fix any uninitialized contracts immediately`);
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Error:", error);
            process.exit(1);
        });
}

export { checkProxyWithUpgrades };
