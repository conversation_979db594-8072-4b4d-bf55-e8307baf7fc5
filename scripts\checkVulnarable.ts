const implAddress = await upgrades.erc1967.getImplementationAddress('******************************************');
const impl = await ethers.getContractAt("StoragePool", implAddress);

// Dummy address = proxy address (safe)
const dummyAddr = tokenProxyAddress;
const dummyMint = ethers.parseEther("0"); // Safe: 0 mint

await impl.initialize(
  dummyAddr,
  dummyAddr,
  "******************************************",
  "******************************************"
);
console.log("✅ Implementation safely initialized with dummy values");
