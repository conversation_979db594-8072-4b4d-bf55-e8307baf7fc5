import { ethers } from "hardhat";
import { listContractAdmins } from './checkVulnarable';

/**
 * Simple test script to verify admin listing functionality
 * 
 * This script tests the admin listing function with your deployed contracts
 * to ensure it correctly retrieves and displays admin information.
 */

async function testAdminListing() {
    console.log("🧪 Testing Admin Listing Functionality");
    console.log("=".repeat(50));
    
    // Your deployed contract addresses
    const testContracts = [
        { 
            address: "******************************************", 
            type: "StorageToken",
            name: "Storage Token"
        },
        { 
            address: "******************************************", 
            type: "StoragePool",
            name: "Storage Pool"
        },
        { 
            address: "******************************************", 
            type: "StakingPool",
            name: "Staking Pool"
        },
        { 
            address: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d", 
            type: "TestnetMiningRewards",
            name: "Testnet Mining Rewards"
        },
        { 
            address: "0x0AF8Bf19C18a3c7352f831cf950CA8971202e4Be", 
            type: "AirdropContract",
            name: "Airdrop Contract"
        },
        { 
            address: "0x0C85A8E992E3Eb04A22027F7E0BC53392A331aC8", 
            type: "TokenDistributionEngine",
            name: "Token Distribution Engine"
        },
    ];
    
    for (const contract of testContracts) {
        console.log(`\n📋 Testing: ${contract.name}`);
        console.log(`📍 Address: ${contract.address}`);
        console.log(`🏷️  Type: ${contract.type}`);
        
        try {
            const admins = await listContractAdmins(contract.address, contract.type);
            
            if (admins.length > 0) {
                console.log(`✅ Found ${admins.length} admin(s):`);
                admins.forEach((admin, index) => {
                    console.log(`   👤 Admin ${index + 1}: ${admin}`);
                });
            } else {
                console.log(`❌ No admins found`);
            }
            
            // Additional verification - check if we can call the contract directly
            try {
                const contract_instance = await ethers.getContractAt(contract.type, contract.address);
                const ADMIN_ROLE = ethers.keccak256(ethers.toUtf8Bytes("ADMIN_ROLE"));
                const adminCount = await contract_instance.getRoleMemberCount(ADMIN_ROLE);
                console.log(`🔍 Direct verification: ${adminCount} admin(s) found`);
                
                if (adminCount.toString() !== admins.length.toString()) {
                    console.log(`⚠️  Mismatch detected! Function returned ${admins.length}, direct call returned ${adminCount}`);
                } else {
                    console.log(`✅ Verification passed!`);
                }
            } catch (verifyError: any) {
                console.log(`❌ Direct verification failed: ${verifyError.message}`);
            }
            
        } catch (error: any) {
            console.log(`❌ Error: ${error.message}`);
        }
        
        console.log("-".repeat(50));
    }
    
    console.log("\n🎯 Test Summary:");
    console.log("- If you see admin addresses listed, the function is working correctly");
    console.log("- If you see 'No admins found', there might be an issue with the contract or network");
    console.log("- If you see verification mismatches, there might be a bug in the function");
    console.log("\n💡 Expected behavior:");
    console.log("- Each contract should have at least 2 admins (based on your adminCount = 2)");
    console.log("- Admin addresses should be valid Ethereum addresses (0x...)");
    console.log("- Verification should pass for all contracts");
}

async function testSingleContract() {
    console.log("🧪 Testing Single Contract (StorageToken)");
    console.log("=".repeat(50));
    
    const address = "******************************************";
    const type = "StorageToken";
    
    try {
        console.log("📋 Getting admins using listContractAdmins function...");
        const admins = await listContractAdmins(address, type);
        console.log(`Found ${admins.length} admins:`, admins);
        
        console.log("\n📋 Getting admins using direct contract call...");
        const contract = await ethers.getContractAt(type, address);
        const ADMIN_ROLE = ethers.keccak256(ethers.toUtf8Bytes("ADMIN_ROLE"));
        const adminCount = await contract.getRoleMemberCount(ADMIN_ROLE);
        
        console.log(`Admin count: ${adminCount}`);
        for (let i = 0; i < adminCount; i++) {
            const admin = await contract.getRoleMember(ADMIN_ROLE, i);
            console.log(`Admin ${i + 1}: ${admin}`);
        }
        
        // Test hasRole function
        console.log("\n📋 Testing hasRole function...");
        for (const admin of admins) {
            const hasRole = await contract.hasRole(ADMIN_ROLE, admin);
            console.log(`${admin} has ADMIN_ROLE: ${hasRole}`);
        }
        
    } catch (error: any) {
        console.error("❌ Error:", error.message);
    }
}

async function main() {
    const testType = process.argv[2];
    
    switch (testType) {
        case "single":
            await testSingleContract();
            break;
        case "all":
        default:
            await testAdminListing();
            break;
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Test failed:", error);
            process.exit(1);
        });
}

export { testAdminListing, testSingleContract };
