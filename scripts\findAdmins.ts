import { ethers } from "hardhat";

/**
 * Enhanced Admin Discovery Script
 * 
 * This script helps identify admin addresses for contracts that use AccessControl
 * but don't have enumeration capabilities. It uses multiple strategies to find admins.
 */

async function findAdminsForContract(contractAddress: string, contractType: string) {
    console.log(`\n🔍 Finding admins for ${contractType} at ${contractAddress}`);
    console.log("=".repeat(60));
    
    try {
        // Get contract instance
        const contract = await ethers.getContractAt(contractType, contractAddress);
        const ADMIN_ROLE = ethers.keccak256(ethers.toUtf8Bytes("ADMIN_ROLE"));
        
        // Get admin count
        const adminCount = await contract.adminCount();
        console.log(`📊 Total admin count: ${adminCount}`);
        
        // Strategy 1: Check known deployment addresses
        console.log("\n🎯 Strategy 1: Checking known deployment addresses...");
        const knownAddresses = [
            "******************************************", // Initial owner from deployments
            "******************************************", // Initial admin from deployments
            "******************************************", // Common test account
            "******************************************", // Common test account
        ];
        
        const confirmedAdmins: string[] = [];
        
        for (const address of knownAddresses) {
            try {
                const hasRole = await contract.hasRole(ADMIN_ROLE, address);
                if (hasRole) {
                    confirmedAdmins.push(address);
                    console.log(`✅ Found admin: ${address}`);
                }
            } catch (error) {
                console.log(`❌ Error checking ${address}`);
            }
        }
        
        // Strategy 2: Check contract owner
        console.log("\n🎯 Strategy 2: Checking contract owner...");
        try {
            const owner = await contract.owner();
            console.log(`👑 Contract owner: ${owner}`);
            
            const ownerHasAdminRole = await contract.hasRole(ADMIN_ROLE, owner);
            if (ownerHasAdminRole && !confirmedAdmins.includes(owner)) {
                confirmedAdmins.push(owner);
                console.log(`✅ Owner is also admin: ${owner}`);
            }
        } catch (error) {
            console.log(`❌ Could not get contract owner`);
        }
        
        // Strategy 3: Look for RoleGranted events (if available)
        console.log("\n🎯 Strategy 3: Searching for RoleGranted events...");
        try {
            // Get recent blocks to search for events
            const currentBlock = await ethers.provider.getBlockNumber();
            const fromBlock = Math.max(0, currentBlock - 10000); // Last 10k blocks
            
            const filter = contract.filters.RoleGranted(ADMIN_ROLE);
            const events = await contract.queryFilter(filter, fromBlock, currentBlock);
            
            console.log(`📅 Found ${events.length} RoleGranted events for ADMIN_ROLE`);
            
            for (const event of events) {
                if (event.args) {
                    const grantedTo = event.args[2]; // account parameter
                    if (!confirmedAdmins.includes(grantedTo)) {
                        // Verify they still have the role
                        const stillHasRole = await contract.hasRole(ADMIN_ROLE, grantedTo);
                        if (stillHasRole) {
                            confirmedAdmins.push(grantedTo);
                            console.log(`✅ Found admin from events: ${grantedTo}`);
                        }
                    }
                }
            }
        } catch (error) {
            console.log(`❌ Could not search events: ${error}`);
        }
        
        // Summary
        console.log("\n📋 SUMMARY:");
        console.log(`📊 Contract reports ${adminCount} total admins`);
        console.log(`✅ Confirmed ${confirmedAdmins.length} admin addresses:`);
        
        confirmedAdmins.forEach((admin, index) => {
            console.log(`   ${index + 1}. ${admin}`);
        });
        
        if (confirmedAdmins.length < Number(adminCount)) {
            const missing = Number(adminCount) - confirmedAdmins.length;
            console.log(`❓ ${missing} admin(s) not identified`);
            console.log(`💡 Tip: Check block explorer for RoleGranted events or deployment transactions`);
        }
        
        return {
            totalCount: Number(adminCount),
            confirmedAdmins: confirmedAdmins
        };
        
    } catch (error: any) {
        console.error(`❌ Error: ${error.message}`);
        return {
            totalCount: 0,
            confirmedAdmins: []
        };
    }
}

async function findAllAdmins() {
    console.log("🔍 COMPREHENSIVE ADMIN DISCOVERY");
    console.log("=".repeat(80));
    
    const contracts = [
        { address: "0x9e12735d77c72c5C3670636D428f2F3815d8A4cB", type: "StorageToken", name: "Storage Token" },
        { address: "0x2e757c35680756cdF8e6AE3f8a346D12b4e3773D", type: "StoragePool", name: "Storage Pool" },
        { address: "0xD8be67B0f4783aa85Ada89863449b9Bc5D79460b", type: "StakingPool", name: "Staking Pool" },
        { address: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d", type: "TestnetMiningRewards", name: "Testnet Mining" },
        { address: "0x0AF8Bf19C18a3c7352f831cf950CA8971202e4Be", type: "AirdropContract", name: "Airdrop" },
        { address: "0x0C85A8E992E3Eb04A22027F7E0BC53392A331aC8", type: "TokenDistributionEngine", name: "Distribution" },
    ];
    
    const allResults: Array<{
        name: string;
        address: string;
        type: string;
        totalCount: number;
        confirmedAdmins: string[];
    }> = [];
    
    for (const contract of contracts) {
        const result = await findAdminsForContract(contract.address, contract.type);
        allResults.push({
            name: contract.name,
            address: contract.address,
            type: contract.type,
            totalCount: result.totalCount,
            confirmedAdmins: result.confirmedAdmins
        });
    }
    
    // Final summary
    console.log("\n" + "=".repeat(80));
    console.log("📊 FINAL ADMIN SUMMARY");
    console.log("=".repeat(80));
    
    let totalAdmins = 0;
    let totalConfirmed = 0;
    
    allResults.forEach((result) => {
        console.log(`\n📋 ${result.name}:`);
        console.log(`   📍 Address: ${result.address}`);
        console.log(`   📊 Total Admins: ${result.totalCount}`);
        console.log(`   ✅ Confirmed: ${result.confirmedAdmins.length}`);
        
        if (result.confirmedAdmins.length > 0) {
            result.confirmedAdmins.forEach((admin, index) => {
                console.log(`      ${index + 1}. ${admin}`);
            });
        }
        
        totalAdmins += result.totalCount;
        totalConfirmed += result.confirmedAdmins.length;
    });
    
    console.log(`\n🎯 OVERALL STATS:`);
    console.log(`   📊 Total admins across all contracts: ${totalAdmins}`);
    console.log(`   ✅ Total confirmed: ${totalConfirmed}`);
    console.log(`   ❓ Unidentified: ${totalAdmins - totalConfirmed}`);
    
    if (totalConfirmed < totalAdmins) {
        console.log(`\n💡 TIPS TO FIND MISSING ADMINS:`);
        console.log(`   1. Check deployment transaction logs on block explorer`);
        console.log(`   2. Look for RoleGranted events in contract history`);
        console.log(`   3. Check if any addresses were added after deployment`);
        console.log(`   4. Verify if some admins were removed (check RoleRevoked events)`);
    }
}

async function main() {
    const mode = process.argv[2];
    
    if (mode === "single") {
        // Test single contract
        const address = process.argv[3] || "0x9e12735d77c72c5C3670636D428f2F3815d8A4cB";
        const type = process.argv[4] || "StorageToken";
        await findAdminsForContract(address, type);
    } else {
        // Find all admins
        await findAllAdmins();
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Error:", error);
            process.exit(1);
        });
}

export { findAdminsForContract, findAllAdmins };
