import { ethers, upgrades } from "hardhat";

/**
 * Implementation Admin Discovery Script
 * 
 * This script uses multiple techniques to discover all admins in implementation contracts,
 * including checking getRoleAdmin, searching events, and testing known addresses.
 */

async function discoverImplementationAdmins(proxyAddress: string, contractType: string) {
    console.log(`🔍 DISCOVERING IMPLEMENTATION ADMINS`);
    console.log(`📍 Proxy: ${proxyAddress}`);
    console.log(`🏷️  Type: ${contractType}`);
    console.log("=".repeat(60));
    
    try {
        // Get implementation address
        const implAddress = await upgrades.erc1967.getImplementationAddress(proxyAddress);
        console.log(`🔧 Implementation: ${implAddress}`);
        
        // Get implementation contract
        const impl = await ethers.getContractAt(contractType, implAddress);
        
        // Role constants
        const ADMIN_ROLE = ethers.keccak256(ethers.toUtf8Bytes("ADMIN_ROLE"));
        const DEFAULT_ADMIN_ROLE = "0x0000000000000000000000000000000000000000000000000000000000000000";
        
        console.log(`\n📊 BASIC INFO:`);
        const adminCount = await impl.adminCount();
        console.log(`   Admin Count: ${adminCount}`);
        
        // Check role admin
        console.log(`\n🔑 ROLE HIERARCHY:`);
        try {
            const roleAdmin = await impl.getRoleAdmin(ADMIN_ROLE);
            console.log(`   ADMIN_ROLE admin: ${roleAdmin}`);
            
            const defaultRoleAdmin = await impl.getRoleAdmin(DEFAULT_ADMIN_ROLE);
            console.log(`   DEFAULT_ADMIN_ROLE admin: ${defaultRoleAdmin}`);
        } catch (error) {
            console.log(`   ❌ Could not get role admin info`);
        }
        
        // Strategy 1: Check known addresses
        console.log(`\n🎯 STRATEGY 1: Testing Known Addresses`);
        const testAddresses = [
            "0x000000000000000000000000000000000000dEaD", // Dead address
            "0x0000000000000000000000000000000000000000", // Zero address
            "0x383a6A34C623C02dcf9BB7069FAE4482967fb713", // Your real admin 1
            "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446", // Your real admin 2
            implAddress, // Implementation itself
            proxyAddress, // Proxy address
        ];
        
        const confirmedAdmins: string[] = [];
        
        for (const address of testAddresses) {
            try {
                const hasAdminRole = await impl.hasRole(ADMIN_ROLE, address);
                const hasDefaultRole = await impl.hasRole(DEFAULT_ADMIN_ROLE, address);
                
                if (hasAdminRole || hasDefaultRole) {
                    confirmedAdmins.push(address);
                    console.log(`   ✅ ${address}:`);
                    if (hasAdminRole) console.log(`      - Has ADMIN_ROLE`);
                    if (hasDefaultRole) console.log(`      - Has DEFAULT_ADMIN_ROLE`);
                } else {
                    console.log(`   ❌ ${address}: No admin roles`);
                }
            } catch (error) {
                console.log(`   ❓ ${address}: Error checking roles`);
            }
        }
        
        // Strategy 2: Search for RoleGranted events
        console.log(`\n🎯 STRATEGY 2: Searching RoleGranted Events`);
        try {
            const currentBlock = await ethers.provider.getBlockNumber();
            const fromBlock = Math.max(0, currentBlock - 50000); // Last 50k blocks
            
            // Search for ADMIN_ROLE grants
            const adminRoleFilter = impl.filters.RoleGranted(ADMIN_ROLE);
            const adminEvents = await impl.queryFilter(adminRoleFilter, fromBlock, currentBlock);
            
            console.log(`   📅 Found ${adminEvents.length} ADMIN_ROLE grant events:`);
            for (const event of adminEvents) {
                if (event.args) {
                    const grantedTo = event.args[2]; // account parameter
                    console.log(`      🎯 Granted to: ${grantedTo} (Block: ${event.blockNumber})`);
                    
                    // Verify they still have the role
                    const stillHasRole = await impl.hasRole(ADMIN_ROLE, grantedTo);
                    console.log(`         Current status: ${stillHasRole ? 'Still has role ✅' : 'Role revoked ❌'}`);
                    
                    if (stillHasRole && !confirmedAdmins.includes(grantedTo)) {
                        confirmedAdmins.push(grantedTo);
                    }
                }
            }
            
            // Search for DEFAULT_ADMIN_ROLE grants
            const defaultRoleFilter = impl.filters.RoleGranted(DEFAULT_ADMIN_ROLE);
            const defaultEvents = await impl.queryFilter(defaultRoleFilter, fromBlock, currentBlock);
            
            console.log(`   📅 Found ${defaultEvents.length} DEFAULT_ADMIN_ROLE grant events:`);
            for (const event of defaultEvents) {
                if (event.args) {
                    const grantedTo = event.args[2];
                    console.log(`      🎯 Granted to: ${grantedTo} (Block: ${event.blockNumber})`);
                    
                    const stillHasRole = await impl.hasRole(DEFAULT_ADMIN_ROLE, grantedTo);
                    console.log(`         Current status: ${stillHasRole ? 'Still has role ✅' : 'Role revoked ❌'}`);
                    
                    if (stillHasRole && !confirmedAdmins.includes(grantedTo)) {
                        confirmedAdmins.push(grantedTo);
                    }
                }
            }
            
        } catch (error: any) {
            console.log(`   ❌ Error searching events: ${error.message}`);
        }
        
        // Strategy 3: Check contract owner
        console.log(`\n🎯 STRATEGY 3: Checking Contract Owner`);
        try {
            const owner = await impl.owner();
            console.log(`   👑 Contract owner: ${owner}`);
            
            const ownerHasAdminRole = await impl.hasRole(ADMIN_ROLE, owner);
            const ownerHasDefaultRole = await impl.hasRole(DEFAULT_ADMIN_ROLE, owner);
            
            if (ownerHasAdminRole || ownerHasDefaultRole) {
                console.log(`   ✅ Owner has admin roles`);
                if (!confirmedAdmins.includes(owner)) {
                    confirmedAdmins.push(owner);
                }
            } else {
                console.log(`   ❌ Owner has no admin roles`);
            }
        } catch (error) {
            console.log(`   ❌ Could not get contract owner`);
        }
        
        // Final Summary
        console.log(`\n📋 FINAL SUMMARY:`);
        console.log(`   📊 Contract reports ${adminCount} admins`);
        console.log(`   ✅ Discovered ${confirmedAdmins.length} unique admin addresses:`);
        
        confirmedAdmins.forEach((admin, index) => {
            console.log(`      ${index + 1}. ${admin}`);
            
            // Add context for known addresses
            if (admin === "0x000000000000000000000000000000000000dEaD") {
                console.log(`         🔒 Dead address (SECURE)`);
            } else if (admin === "0x0000000000000000000000000000000000000000") {
                console.log(`         ⚠️  Zero address`);
            } else if (admin === "0x383a6A34C623C02dcf9BB7069FAE4482967fb713" || 
                      admin === "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446") {
                console.log(`         ⚠️  Real admin address (SHOULD NOT BE HERE)`);
            } else if (admin === implAddress) {
                console.log(`         🔧 Implementation contract itself`);
            } else if (admin === proxyAddress) {
                console.log(`         📍 Proxy contract address`);
            }
        });
        
        // Security Assessment
        console.log(`\n🛡️  SECURITY ASSESSMENT:`);
        const hasDeadAddress = confirmedAdmins.includes("0x000000000000000000000000000000000000dEaD");
        const hasRealAdmins = confirmedAdmins.some(addr => 
            addr === "0x383a6A34C623C02dcf9BB7069FAE4482967fb713" || 
            addr === "0xFa8b02596a84F3b81B4144eA2F30482f8C33D446"
        );
        
        if (hasDeadAddress && !hasRealAdmins) {
            console.log(`   ✅ SECURE: Implementation has dead address admin, no real admins`);
        } else if (hasRealAdmins) {
            console.log(`   ⚠️  RISK: Implementation has real admin addresses`);
        } else {
            console.log(`   ❓ UNCLEAR: No dead address found, check manually`);
        }
        
        // Discrepancy check
        if (confirmedAdmins.length !== Number(adminCount)) {
            console.log(`\n💡 NOTE: adminCount (${adminCount}) != discovered admins (${confirmedAdmins.length})`);
            console.log(`   This is normal if the same address was granted multiple roles`);
            console.log(`   or if adminCount is hardcoded to 2 regardless of actual unique admins`);
        }
        
        return {
            adminCount: Number(adminCount),
            discoveredAdmins: confirmedAdmins,
            isSecure: hasDeadAddress && !hasRealAdmins
        };
        
    } catch (error: any) {
        console.error(`❌ Error: ${error.message}`);
        return {
            adminCount: 0,
            discoveredAdmins: [],
            isSecure: false
        };
    }
}

async function checkAllImplementations() {
    console.log("🔍 COMPREHENSIVE IMPLEMENTATION ADMIN DISCOVERY");
    console.log("=".repeat(80));
    
    const contracts = [
        { address: "0x9e12735d77c72c5C3670636D428f2F3815d8A4cB", type: "StorageToken", name: "Storage Token" },
        { address: "0x2e757c35680756cdF8e6AE3f8a346D12b4e3773D", type: "StoragePool", name: "Storage Pool" },
        { address: "0xD8be67B0f4783aa85Ada89863449b9Bc5D79460b", type: "StakingPool", name: "Staking Pool" },
        { address: "0x1Def7229f6d6Ca5fbA4f9e28Cd1cf4e2688e545d", type: "TestnetMiningRewards", name: "Testnet Mining" },
        { address: "0x0AF8Bf19C18a3c7352f831cf950CA8971202e4Be", type: "AirdropContract", name: "Airdrop" },
        { address: "0x0C85A8E992E3Eb04A22027F7E0BC53392A331aC8", type: "TokenDistributionEngine", name: "Distribution" },
    ];
    
    const results = [];
    
    for (const contract of contracts) {
        console.log(`\n${"=".repeat(60)}`);
        console.log(`📋 ${contract.name}`);
        
        const result = await discoverImplementationAdmins(contract.address, contract.type);
        results.push({
            name: contract.name,
            ...result
        });
    }
    
    // Overall summary
    console.log(`\n${"=".repeat(80)}`);
    console.log("🎯 OVERALL SECURITY STATUS");
    console.log("=".repeat(80));
    
    const secureCount = results.filter(r => r.isSecure).length;
    const totalCount = results.length;
    
    console.log(`✅ Secure implementations: ${secureCount}/${totalCount}`);
    
    results.forEach(result => {
        const status = result.isSecure ? "✅ SECURE" : "⚠️  NEEDS REVIEW";
        console.log(`   ${result.name}: ${status}`);
    });
}

async function main() {
    const mode = process.argv[2];
    
    if (mode === "single") {
        const address = process.argv[3] || "0x9e12735d77c72c5C3670636D428f2F3815d8A4cB";
        const type = process.argv[4] || "StorageToken";
        await discoverImplementationAdmins(address, type);
    } else {
        await checkAllImplementations();
    }
}

if (require.main === module) {
    main()
        .then(() => process.exit(0))
        .catch((error) => {
            console.error("💥 Error:", error);
            process.exit(1);
        });
}

export { discoverImplementationAdmins, checkAllImplementations };
