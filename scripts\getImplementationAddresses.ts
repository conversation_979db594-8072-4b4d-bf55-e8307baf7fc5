import { ethers, upgrades } from "hardhat";

async function main() {
    // Replace these with your actual proxy addresses
    const storagePoolProxy = process.env.STORAGE_POOL_PROXY || "******************************************";
    const stakingPoolProxy = process.env.STAKING_POOL_PROXY || "******************************************";

    console.log("Getting implementation addresses...");
    
    try {
        const storagePoolImpl = await upgrades.erc1967.getImplementationAddress(storagePoolProxy);
        console.log("StoragePool proxy:", storagePoolProxy);
        console.log("StoragePool implementation:", storagePoolImpl);
        
        const stakingPoolImpl = await upgrades.erc1967.getImplementationAddress(stakingPoolProxy);
        console.log("StakingPool proxy:", stakingPoolProxy);
        console.log("StakingPool implementation:", stakingPoolImpl);
        
        console.log("\nVerification commands:");
        console.log(`npx hardhat verify --network base ${storagePoolImpl}`);
        console.log(`npx hardhat verify --network base ${stakingPoolImpl}`);
        
    } catch (error: any) {
        console.error("Error getting implementation addresses:", error.message);
    }
}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
});
